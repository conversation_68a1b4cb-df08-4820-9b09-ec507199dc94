import { LL<PERSON>rovider, AgentContext } from '../types';
export declare class AgentOrchestrator {
    private static instance;
    private logger;
    private configManager;
    private llmProviderFactory;
    private toolRegistry;
    private sessionManager;
    private contextEngine;
    private currentProvider;
    private constructor();
    static getInstance(): AgentOrchestrator;
    initialize(workingDirectory: string): Promise<AgentContext>;
    processUserInput(input: string, context: AgentContext, options?: {
        streaming?: boolean;
        maxIterations?: number;
        enableToolCalling?: boolean;
    }): Promise<string>;
    private getLLMResponse;
    private buildSystemPrompt;
    private executeTools;
    private summarizeToolResults;
    switchProvider(providerName: string): Promise<void>;
    getCurrentProvider(): LLMProvider | null;
    getProviderStatus(): Promise<{
        current: string;
        available: string[];
        working: Record<string, boolean>;
    }>;
    getSessionInfo(): {
        sessionId: string;
        messageCount: number;
        workingDirectory: string;
        projectType: string;
    } | null;
    clearConversation(): Promise<void>;
    refreshProjectContext(): Promise<void>;
    executeAutonomousTask(task: string, workingDirectory: string, options?: {
        maxIterations?: number;
        enableLearning?: boolean;
        saveProgress?: boolean;
    }): Promise<{
        success: boolean;
        iterations: number;
        results: any[];
        finalResponse: string;
    }>;
    private evaluateTaskCompletion;
    private generateNextIteration;
    private saveTaskProgress;
    private saveTaskCompletion;
    analyzeProjectHealth(workingDirectory: string): Promise<{
        score: number;
        issues: string[];
        recommendations: string[];
        metrics: Record<string, any>;
    }>;
    cleanup(): void;
}
//# sourceMappingURL=AgentOrchestrator.d.ts.map