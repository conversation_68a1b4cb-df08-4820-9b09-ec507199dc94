#!/usr/bin/env node

import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import dotenv from 'dotenv';
import path from 'path';
import { AgentOrchestrator } from './agents/AgentOrchestrator';
import { ConfigManager } from './config/ConfigManager';
import { SessionManager } from './session/SessionManager';
import { ErrorHandler } from './utils/ErrorHandler';
import { Logger } from './utils/Logger';

// Load environment variables
dotenv.config();

const program = new Command();
// const logger = Logger.getInstance();
const errorHandler = ErrorHandler.getInstance();
const configManager = ConfigManager.getInstance();
const sessionManager = SessionManager.getInstance();
const agentOrchestrator = AgentOrchestrator.getInstance();

// Setup graceful shutdown
errorHandler.createGracefulShutdown();

program
  .name('ai-cli')
  .description('Autonomous AI-Powered CLI Tool System')
  .version('1.0.0');

// Main chat command
program
  .command('chat')
  .description('Start an interactive AI chat session')
  .option('-d, --directory <path>', 'Working directory', process.cwd())
  .option('-p, --provider <provider>', 'LLM provider to use')
  .option('-m, --model <model>', 'Model to use')
  .option('-s, --session <sessionId>', 'Resume existing session')
  .option('--autonomous', 'Enable autonomous mode for complex tasks')
  .option('--max-iterations <iterations>', 'Maximum iterations for autonomous mode', parseInt)
  .action(async (options) => {
    const spinner = ora('Initializing AI CLI...').start();
    
    try {
      // Set working directory
      const workingDirectory = path.resolve(options.directory);
      process.chdir(workingDirectory);

      // Update config if provider/model specified
      if (options.provider) {
        configManager.updateAgentConfig({ provider: options.provider });
      }
      if (options.model) {
        configManager.updateAgentConfig({ model: options.model });
      }

      // Validate configuration
      if (!configManager.validateConfig()) {
        spinner.fail('Configuration validation failed');
        console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
        return;
      }

      // Initialize or resume session
      let context;
      if (options.session) {
        spinner.text = 'Resuming session...';
        const session = await sessionManager.loadSession(options.session);
        context = session.context;
      } else {
        spinner.text = 'Creating new session...';
        context = await agentOrchestrator.initialize(workingDirectory);
      }

      spinner.succeed('AI CLI initialized successfully!');

      // Display welcome message
      console.log(chalk.blue.bold('\n🤖 AI CLI Agent - Autonomous Assistant'));
      console.log(chalk.gray('Type "help" for commands, "exit" to quit\n'));

      const sessionInfo = agentOrchestrator.getSessionInfo();
      if (sessionInfo) {
        console.log(chalk.cyan(`📁 Working Directory: ${sessionInfo.workingDirectory}`));
        console.log(chalk.cyan(`🏗️  Project Type: ${sessionInfo.projectType}`));
        console.log(chalk.cyan(`💬 Session: ${sessionInfo.sessionId.substring(0, 8)}...`));
        console.log('');
      }

      // Start interactive chat loop
      await startChatLoop(context);

    } catch (error) {
      spinner.fail('Failed to initialize AI CLI');
      errorHandler.handleError(error as Error, 'CLI initialization');
    }
  });

// Configuration command
program
  .command('config')
  .description('Configure AI CLI settings')
  .option('--provider <provider>', 'Set default LLM provider')
  .option('--model <model>', 'Set default model')
  .option('--api-key <key>', 'Set API key for current provider')
  .option('--list', 'List current configuration')
  .action(async (options) => {
    try {
      if (options.list) {
        const config = configManager.getConfig();
        console.log(chalk.blue.bold('\n🔧 Current Configuration:'));
        console.log(chalk.cyan(`Provider: ${config.agent.provider}`));
        console.log(chalk.cyan(`Model: ${config.agent.model}`));
        console.log(chalk.cyan(`Temperature: ${config.agent.temperature}`));
        console.log(chalk.cyan(`Max Tokens: ${config.agent.maxTokens}`));
        console.log(chalk.cyan(`Tool Calling: ${config.agent.enableToolCalling ? 'Enabled' : 'Disabled'}`));
        console.log(chalk.cyan(`Parallel Execution: ${config.agent.enableParallelExecution ? 'Enabled' : 'Disabled'}`));
        return;
      }

      if (options.provider) {
        configManager.updateAgentConfig({ provider: options.provider });
        console.log(chalk.green(`✅ Provider set to: ${options.provider}`));
      }

      if (options.model) {
        configManager.updateAgentConfig({ model: options.model });
        console.log(chalk.green(`✅ Model set to: ${options.model}`));
      }

      if (options.apiKey) {
        const provider = options.provider || configManager.getAgentConfig().provider;
        configManager.setProviderApiKey(provider, options.apiKey);
        console.log(chalk.green(`✅ API key set for: ${provider}`));
      }

      if (!options.provider && !options.model && !options.apiKey) {
        await interactiveConfig();
      }

    } catch (error) {
      errorHandler.handleError(error as Error, 'Configuration');
    }
  });

// Session management commands
program
  .command('sessions')
  .description('Manage chat sessions')
  .option('--list', 'List all sessions')
  .option('--delete <sessionId>', 'Delete a session')
  .option('--export <sessionId>', 'Export a session')
  .option('--import <filePath>', 'Import a session')
  .action(async (options) => {
    try {
      if (options.list) {
        const sessions = sessionManager.listSessions();
        console.log(chalk.blue.bold('\n📝 Chat Sessions:'));
        
        if (sessions.length === 0) {
          console.log(chalk.gray('No sessions found.'));
          return;
        }

        for (const session of sessions.slice(0, 10)) {
          const age = Math.floor((Date.now() - session.lastAccessedAt.getTime()) / (1000 * 60 * 60));
          console.log(chalk.cyan(`${session.id.substring(0, 8)}... - ${session.workingDirectory} (${age}h ago)`));
        }
        return;
      }

      if (options.delete) {
        const deleted = await sessionManager.deleteSession(options.delete);
        if (deleted) {
          console.log(chalk.green(`✅ Session deleted: ${options.delete}`));
        } else {
          console.log(chalk.red(`❌ Session not found: ${options.delete}`));
        }
        return;
      }

      if (options.export) {
        const filePath = `session-${options.export.substring(0, 8)}.json`;
        await sessionManager.exportSession(options.export, filePath);
        console.log(chalk.green(`✅ Session exported to: ${filePath}`));
        return;
      }

      if (options.import) {
        const session = await sessionManager.importSession(options.import);
        console.log(chalk.green(`✅ Session imported: ${session.id}`));
        return;
      }

    } catch (error) {
      errorHandler.handleError(error as Error, 'Session management');
    }
  });

// Status command
program
  .command('status')
  .description('Show AI CLI status and diagnostics')
  .action(async () => {
    try {
      const spinner = ora('Checking status...').start();
      
      const providerStatus = await agentOrchestrator.getProviderStatus();
      const sessionStats = sessionManager.getSessionStats();
      
      spinner.succeed('Status check completed');
      
      console.log(chalk.blue.bold('\n🔍 AI CLI Status:'));
      console.log(chalk.cyan(`Current Provider: ${providerStatus.current}`));
      console.log(chalk.cyan(`Available Providers: ${providerStatus.available.join(', ')}`));
      
      console.log(chalk.blue.bold('\n🔌 Provider Status:'));
      for (const [provider, working] of Object.entries(providerStatus.working)) {
        const status = working ? chalk.green('✅ Working') : chalk.red('❌ Not working');
        console.log(chalk.cyan(`${provider}: ${status}`));
      }
      
      console.log(chalk.blue.bold('\n📊 Session Statistics:'));
      console.log(chalk.cyan(`Total Sessions: ${sessionStats.totalSessions}`));
      console.log(chalk.cyan(`Active Sessions: ${sessionStats.activeSessions}`));
      console.log(chalk.cyan(`Total Messages: ${sessionStats.totalMessages}`));
      
    } catch (error) {
      errorHandler.handleError(error as Error, 'Status check');
    }
  });

// Autonomous task execution command
program
  .command('task <description>')
  .description('Execute an autonomous task')
  .option('-d, --directory <path>', 'Working directory', process.cwd())
  .option('-p, --provider <provider>', 'LLM provider to use')
  .option('--max-iterations <iterations>', 'Maximum iterations', parseInt, 10)
  .option('--save-progress', 'Save task progress')
  .option('--learning', 'Enable learning mode')
  .action(async (description, options) => {
    const spinner = ora('Initializing autonomous task...').start();

    try {
      const workingDirectory = path.resolve(options.directory);

      if (options.provider) {
        configManager.updateAgentConfig({ provider: options.provider });
      }

      if (!configManager.validateConfig()) {
        spinner.fail('Configuration validation failed');
        console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
        return;
      }

      await agentOrchestrator.initialize(workingDirectory);

      spinner.text = 'Executing autonomous task...';

      const result = await agentOrchestrator.executeAutonomousTask(description, workingDirectory, {
        maxIterations: options.maxIterations,
        enableLearning: options.learning,
        saveProgress: options.saveProgress
      });

      spinner.succeed(`Task completed in ${result.iterations} iterations`);

      console.log(chalk.blue.bold('\n🤖 Autonomous Task Results:'));
      console.log(chalk.cyan(`Success: ${result.success ? '✅' : '❌'}`));
      console.log(chalk.cyan(`Iterations: ${result.iterations}`));
      console.log(chalk.cyan(`Final Response:`));
      console.log(chalk.white(result.finalResponse));

      if (result.results.length > 0) {
        console.log(chalk.blue.bold('\n📊 Execution Summary:'));
        result.results.forEach((res, i) => {
          console.log(chalk.gray(`${i + 1}. ${res.task.substring(0, 80)}...`));
        });
      }

    } catch (error) {
      spinner.fail('Autonomous task failed');
      errorHandler.handleError(error as Error, 'Autonomous task execution');
    }
  });

// Project analysis command
program
  .command('analyze [directory]')
  .description('Analyze project health and structure')
  .option('--detailed', 'Show detailed analysis')
  .action(async (directory, options) => {
    const spinner = ora('Analyzing project...').start();

    try {
      const workingDirectory = path.resolve(directory || process.cwd());

      await agentOrchestrator.initialize(workingDirectory);

      const health = await agentOrchestrator.analyzeProjectHealth(workingDirectory);

      spinner.succeed('Project analysis completed');

      console.log(chalk.blue.bold('\n🔍 Project Health Analysis:'));
      console.log(chalk.cyan(`Health Score: ${health.score}/100`));

      if (health.issues.length > 0) {
        console.log(chalk.red.bold('\n⚠️  Issues Found:'));
        health.issues.forEach(issue => {
          console.log(chalk.red(`• ${issue}`));
        });
      }

      if (health.recommendations.length > 0) {
        console.log(chalk.yellow.bold('\n💡 Recommendations:'));
        health.recommendations.forEach(rec => {
          console.log(chalk.yellow(`• ${rec}`));
        });
      }

      if (options.detailed) {
        console.log(chalk.blue.bold('\n📈 Metrics:'));
        Object.entries(health.metrics).forEach(([key, value]) => {
          console.log(chalk.cyan(`${key}: ${value}`));
        });
      }

    } catch (error) {
      spinner.fail('Project analysis failed');
      errorHandler.handleError(error as Error, 'Project analysis');
    }
  });

// Context management command
program
  .command('context')
  .description('Manage project context')
  .option('--refresh', 'Refresh project context')
  .option('--snapshot', 'Save context snapshot')
  .option('--history', 'Show context history')
  .option('--restore <index>', 'Restore from snapshot', parseInt)
  .option('-d, --directory <path>', 'Working directory', process.cwd())
  .action(async (options) => {
    try {
      const workingDirectory = path.resolve(options.directory);
      await agentOrchestrator.initialize(workingDirectory);

      if (options.refresh) {
        const spinner = ora('Refreshing context...').start();
        await agentOrchestrator.refreshProjectContext();
        spinner.succeed('Context refreshed');
        return;
      }

      if (options.snapshot) {
        const spinner = ora('Saving context snapshot...').start();
        const contextEngine = (agentOrchestrator as any).contextEngine;
        await contextEngine.saveContextSnapshot(workingDirectory);
        spinner.succeed('Context snapshot saved');
        return;
      }

      if (options.history) {
        const contextEngine = (agentOrchestrator as any).contextEngine;
        const history = contextEngine.getContextHistory(workingDirectory);

        console.log(chalk.blue.bold('\n📚 Context History:'));
        if (history.length === 0) {
          console.log(chalk.gray('No context snapshots found.'));
        } else {
          history.forEach((snapshot: any, index: number) => {
            const date = new Date(snapshot.lastUpdated).toLocaleString();
            console.log(chalk.cyan(`${index}: ${date} - ${snapshot.files?.length || 0} files`));
          });
        }
        return;
      }

      if (typeof options.restore === 'number') {
        const spinner = ora('Restoring context snapshot...').start();
        const contextEngine = (agentOrchestrator as any).contextEngine;
        const restored = contextEngine.restoreContextSnapshot(workingDirectory, options.restore);

        if (restored) {
          spinner.succeed(`Context restored from snapshot ${options.restore}`);
        } else {
          spinner.fail(`Failed to restore snapshot ${options.restore}`);
        }
        return;
      }

      // Show context summary
      const contextEngine = (agentOrchestrator as any).contextEngine;
      const context = contextEngine.getProjectContext(workingDirectory);
      const metrics = contextEngine.getContextMetrics(workingDirectory);

      console.log(chalk.blue.bold('\n📁 Context Summary:'));
      if (context) {
        console.log(chalk.cyan(`Project Type: ${context.projectType}`));
        console.log(chalk.cyan(`Files: ${context.files.length}`));
        console.log(chalk.cyan(`Dependencies: ${Object.keys(context.dependencies).length}`));
        console.log(chalk.cyan(`Git: ${context.gitInfo ? 'Yes' : 'No'}`));
      }

      if (metrics) {
        console.log(chalk.blue.bold('\n📊 Context Metrics:'));
        console.log(chalk.cyan(`File Count: ${metrics.fileCount}`));
        console.log(chalk.cyan(`Total Size: ${(metrics.totalSize / 1024 / 1024).toFixed(2)} MB`));
        console.log(chalk.cyan(`Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%`));
        console.log(chalk.cyan(`Last Updated: ${metrics.lastUpdated.toLocaleString()}`));
      }

    } catch (error) {
      errorHandler.handleError(error as Error, 'Context management');
    }
  });

// Tool statistics command
program
  .command('tools')
  .description('Show tool usage statistics and information')
  .option('--stats', 'Show usage statistics')
  .option('--list', 'List available tools')
  .action(async (options) => {
    try {
      await agentOrchestrator.initialize(process.cwd());
      const toolRegistry = (agentOrchestrator as any).toolRegistry;

      if (options.stats) {
        const stats = toolRegistry.getToolUsageStats();
        console.log(chalk.blue.bold('\n🔧 Tool Usage Statistics:'));

        Object.entries(stats).forEach(([toolName, stat]: [string, any]) => {
          const successRate = stat.calls > 0 ? ((stat.successes / stat.calls) * 100).toFixed(1) : '0';
          console.log(chalk.cyan(`\n${toolName}:`));
          console.log(chalk.gray(`  Calls: ${stat.calls}`));
          console.log(chalk.gray(`  Success Rate: ${successRate}%`));
          console.log(chalk.gray(`  Average Duration: ${stat.averageDuration.toFixed(0)}ms`));
        });
        return;
      }

      if (options.list) {
        const tools = toolRegistry.getAllTools();
        console.log(chalk.blue.bold('\n🛠️  Available Tools:'));

        tools.forEach((tool: any) => {
          console.log(chalk.cyan(`\n${tool.name}:`));
          console.log(chalk.gray(`  Description: ${tool.description}`));
          console.log(chalk.gray(`  Parameters: ${Object.keys(tool.parameters.properties || {}).join(', ')}`));
        });
        return;
      }

      // Default: show both
      const tools = toolRegistry.getAllTools();
      const stats = toolRegistry.getToolUsageStats();

      console.log(chalk.blue.bold('\n🛠️  Tool Overview:'));
      console.log(chalk.cyan(`Total Tools: ${tools.length}`));

      const totalCalls = Object.values(stats).reduce((sum: number, stat: any) => sum + stat.calls, 0);
      console.log(chalk.cyan(`Total Executions: ${totalCalls}`));

    } catch (error) {
      errorHandler.handleError(error as Error, 'Tool statistics');
    }
  });

async function startChatLoop(context: any): Promise<void> {
  while (true) {
    try {
      const { input } = await inquirer.prompt({
        type: 'input',
        name: 'input',
        message: chalk.green('You:')
      });

      if (!input.trim()) continue;

      // Handle special commands
      if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
        console.log(chalk.yellow('👋 Goodbye!'));
        break;
      }

      if (input.toLowerCase() === 'help') {
        showHelpMessage();
        continue;
      }

      if (input.toLowerCase() === 'clear') {
        await agentOrchestrator.clearConversation();
        console.log(chalk.yellow('🧹 Conversation cleared'));
        continue;
      }

      if (input.toLowerCase() === 'refresh') {
        await agentOrchestrator.refreshProjectContext();
        console.log(chalk.yellow('🔄 Project context refreshed'));
        continue;
      }

      if (input.toLowerCase().startsWith('autonomous:')) {
        const task = input.substring(11).trim();
        if (task) {
          const spinner = ora('Executing autonomous task...').start();
          try {
            const result = await agentOrchestrator.executeAutonomousTask(task, context.workingDirectory, {
              maxIterations: 5,
              enableLearning: true,
              saveProgress: true
            });

            spinner.succeed(`Autonomous task completed in ${result.iterations} iterations`);
            console.log(chalk.blue('\nAI (Autonomous):'), result.finalResponse);
            console.log('');
          } catch (error) {
            spinner.fail('Autonomous task failed');
            errorHandler.handleError(error as Error, 'Autonomous task');
          }
          continue;
        }
      }

      // Process user input
      const spinner = ora('AI is thinking...').start();
      
      try {
        const response = await agentOrchestrator.processUserInput(input, context, {
          enableToolCalling: true,
          maxIterations: 5
        });

        spinner.stop();
        console.log(chalk.blue('\nAI:'), response);
        console.log('');

      } catch (error) {
        spinner.fail('AI processing failed');
        errorHandler.handleError(error as Error, 'AI processing');
      }

    } catch (error) {
      if ((error as any).name === 'ExitPromptError') {
        console.log(chalk.yellow('\n👋 Goodbye!'));
        break;
      }
      errorHandler.handleError(error as Error, 'Chat loop');
    }
  }

  // Cleanup
  agentOrchestrator.cleanup();
}

function showHelpMessage(): void {
  console.log(chalk.blue.bold('\n🆘 Available Commands:'));
  console.log(chalk.cyan('help                    - Show this help message'));
  console.log(chalk.cyan('clear                   - Clear conversation history'));
  console.log(chalk.cyan('refresh                 - Refresh project context'));
  console.log(chalk.cyan('autonomous: <task>      - Execute autonomous task'));
  console.log(chalk.cyan('exit                    - Exit the chat session'));
  console.log(chalk.gray('\nOr just type your question/request naturally!'));
  console.log(chalk.blue.bold('\n🤖 Autonomous Mode:'));
  console.log(chalk.gray('Use "autonomous: <task description>" for complex multi-step tasks'));
  console.log(chalk.gray('Example: autonomous: analyze this codebase and suggest improvements'));
  console.log('');
}

async function interactiveConfig(): Promise<void> {
  console.log(chalk.blue.bold('\n🔧 Interactive Configuration'));
  
  const { provider } = await inquirer.prompt([
    {
      type: 'list',
      name: 'provider',
      message: 'Select LLM provider:',
      choices: ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral']
    }
  ]);

  configManager.updateAgentConfig({ provider });

  if (provider !== 'ollama') {
    const { apiKey } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter API key for ${provider}:`,
        mask: '*'
      }
    ]);

    configManager.setProviderApiKey(provider, apiKey);
  }

  console.log(chalk.green('\n✅ Configuration saved successfully!'));
}

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
