{"name": "ai-cli-agent", "version": "1.0.0", "description": "Autonomous AI-Powered CLI Tool System with advanced agent capabilities", "main": "dist/cli.js", "bin": {"ai-cli": "dist/cli.js"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx src/cli.ts", "start": "node dist/cli.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prepare": "npm run build"}, "keywords": ["ai", "cli", "agent", "autonomous", "llm", "tools", "automation"], "author": "AI CLI Agent", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "@google/generative-ai": "^0.21.0", "archiver": "^7.0.1", "axios": "^1.7.7", "chalk": "^5.3.0", "chokidar": "^3.6.0", "commander": "^12.1.0", "dotenv": "^16.4.5", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "inquirer": "^10.2.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "node-fetch": "^3.3.2", "openai": "^4.67.3", "ora": "^8.1.0", "semver": "^7.6.3", "simple-git": "^3.25.0", "toml": "^3.0.0", "unzipper": "^0.12.3", "uuid": "^10.0.0", "winston": "^3.15.0", "yaml": "^2.6.0"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.12", "@types/mime-types": "^2.1.4", "@types/node": "^22.9.0", "@types/semver": "^7.5.8", "@types/unzipper": "^0.10.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint": "^9.14.0", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "tsc-alias": "^1.8.16", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-cli-agent.git"}, "bugs": {"url": "https://github.com/your-username/ai-cli-agent/issues"}, "homepage": "https://github.com/your-username/ai-cli-agent#readme"}